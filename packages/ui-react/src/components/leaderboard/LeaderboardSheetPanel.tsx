import { useState } from "react"
import { cn } from "../../lib/utils"
import { ScrollArea } from "../ui/scroll-area"
import { SheetBottomPanelContent, SheetOverlay, SheetPortal } from "../ui/sheet"
import { LeaderboardOverview } from "./LeaderboardOverview"
import { LeaderboardsView } from "./LeaderboardsView"

interface LeaderboardSheetPanelProps {
  portalContainer: HTMLElement
}

export function LeaderboardSheetPanel({ portalContainer }: LeaderboardSheetPanelProps) {
  const [viewingLeaderboardId, setViewingLeaderboardId] = useState<string | null>(null)
  const [showPartner, setShowPartner] = useState(false)

  return (
    <SheetPortal container={portalContainer}>
      <SheetOverlay className="!absolute !inset-0 !bg-black/60" />
      <SheetBottomPanelContent className={cn("!h-[70%]", "!max-h-full", "p-0")}>
        <ScrollArea className="h-full w-full rounded-t-[16px] overflow-hidden">
          {viewingLeaderboardId ? (
            <LeaderboardOverview
              leaderboardId={viewingLeaderboardId}
              onBack={() => setViewingLeaderboardId(null)}
            />
          ) : (
            <LeaderboardsView
              onViewOverview={setViewingLeaderboardId}
              showPartner={showPartner}
              setShowPartner={setShowPartner}
            />
          )}
        </ScrollArea>
      </SheetBottomPanelContent>
    </SheetPortal>
  )
}
