# BetSwirl UI - React Casino Game Components

This is a **game widget library** for BetSwirl protocol casino games built with React + TypeScript + Vite.

## Available Games

* 🪙 **CoinToss** - Classic heads/tails game with animated coin flip
* 🎲 **Dice** - Roll the dice with customizable win conditions
* 🎰 **Roulette** - European roulette with single zero
* 🎱 **Keno** - Pick your lucky numbers and win big
* 🎡 **Wheel** - Spin the wheel of fortune

## Quick Start

🚀 **Build a Web3 casino with just 20 lines of React code!**

* 📖 [React Integration Guide](https://github.com/BetSwirl/sdk/blob/main/packages/ui-react/docs/react-guide.md) - Step-by-step tutorial
* 🎮 [Live Demo](https://ui-react-demo.vercel.app) - See it in action ([source code](https://github.com/BetSwirl/ui-react-demo))
* 🎨 [Storybook](http://demo.betswirl-sdk.chainhackers.xyz/) - Explore all components interactively

🪐 **Create a Farcaster Mini App with <PERSON><PERSON>wirl game**

* 📖 [Farcaster Integration Guide](https://github.com/BetSwirl/sdk/blob/main/packages/ui-react/docs/farcaster-miniapp-guide.md)
* 🎮 [Live Demo](https://miniapp-ui-react-demo.vercel.app) - See it in action ([source code](https://github.com/BetSwirl/miniapp-ui-react-demo))

## Installation

```shell
npm i @betswirl/ui-react
```

## Features

- 🎯 **5 Casino Games** - CoinToss, Dice, Roulette, Keno, Wheel
- ⚡ **Fast Integration** - Get started in minutes
- 🎨 **Customizable** - Built with Tailwind CSS
- 🔗 **Web3 Ready** - Works with any wallet provider
- 📱 **Mobile Friendly** - Responsive design
- 🧪 **TypeScript** - Full type safety

## Development

For contributors and maintainers, see [DEVELOPMENT.md](./DEVELOPMENT.md)
