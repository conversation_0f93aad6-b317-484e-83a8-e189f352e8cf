BASE_URL=`http://localhost:5173`

VITE_AFFILIATE_ADDRESS=0x0000000000000000000000000000000000000000

# RPC URLs for each chain
# You can use public RPCs or get your own from providers like Alchemy, Infura, QuickNode
VITE_BASE_RPC_URL=https://mainnet.base.org
VITE_POLYGON_RPC_URL=https://polygon-rpc.com
VITE_AVALANCHE_RPC_URL=https://api.avax.network/ext/bc/C/rpc
VITE_ARBITRUM_RPC_URL=https://arb1.arbitrum.io/rpc

# Or use premium RPC providers (recommended):
# VITE_BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/YOUR_API_KEY
# VITE_POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/YOUR_API_KEY
# VITE_AVALANCHE_RPC_URL=https://avalanche-mainnet.infura.io/v3/YOUR_API_KEY
# VITE_ARBITRUM_RPC_URL=https://arbitrum-mainnet.infura.io/v3/YOUR_API_KEY

# Note: For E2E testing secrets (SEED_PHRASE, WALLET_PASSWORD), see .secrets.example
