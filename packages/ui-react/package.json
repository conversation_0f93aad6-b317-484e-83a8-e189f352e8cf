{"name": "@betswirl/ui-react", "repository": {"type": "git", "url": "https://github.com/betswirl/sdk.git", "directory": "packages/ui-react"}, "version": "0.1.9", "license": "MIT", "type": "module", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./styles.css": "./dist/index.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "rimraf dist && tsc -b tsconfig.prod.json && vite build", "lint": "biome check .", "lint:fix": "biome check . --write", "format": "biome format . --write", "preview": "vite preview", "storybook": "storybook dev -p 6006", "prebuild-storybook": "pnpm -r --filter @betswirl/sdk-core --filter @betswirl/wagmi-provider run build", "build-storybook": "storybook build", "chromatic": "chromatic --exit-zero-on-changes", "prepublishOnly": "pnpm build", "test:e2e-setup": "pnpm exec playwright install chromium && pnpm exec synpress", "test:e2e": "pnpm exec playwright test --reporter=line", "test:clear-cache": "rm -rf test-results .cache-synpress ~/.cache-synpress 2>/dev/null || true", "test:cointoss": "pnpm exec playwright test tests/coinToss.spec.ts --reporter=line", "test:dice": "pnpm exec playwright test tests/dice.spec.ts --reporter=line", "test:roulette": "pnpm exec playwright test tests/roulette.spec.ts --reporter=line", "test:keno": "pnpm exec playwright test tests/keno.spec.ts --reporter=line", "test:wheel": "pnpm exec playwright test tests/wheel.spec.ts --reporter=line", "test:chain-switching": "pnpm exec playwright test tests/chainSwitching.spec.ts --reporter=line", "test:token-selection": "pnpm exec playwright test tests/tokenSelection.spec.ts --reporter=line", "test:chain-token-list": "pnpm exec playwright test tests/chainTokenList.spec.ts --reporter=line"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "decimal.js": "^10.6.0", "lucide-react": "^0.526.0"}, "peerDependencies": {"@betswirl/sdk-core": "workspace:*", "@betswirl/wagmi-provider": "workspace:*", "@coinbase/onchainkit": "^0.38.15", "@tanstack/react-query": "^5.81.5", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0", "viem": "catalog:", "wagmi": "^2.15.6"}, "devDependencies": {"@betswirl/wagmi-provider": "workspace:*", "@biomejs/biome": "^2.0.6", "@chromatic-com/storybook": "^4.1.0", "@playwright/test": "^1.54.1", "@storybook/addon-docs": "^9.1.1", "@storybook/react-vite": "9.1.1", "@synthetixio/synpress": "^4.1.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "chromatic": "^13.1.2", "dotenv": "^17.2.1", "globals": "^16.3.0", "playwright": "^1.54.1", "postcss": "^8.5.5", "postcss-import": "^16.1.0", "rimraf": "^6.0.1", "storybook": "9.1.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "vite": "^7.0.6", "vitest": "^3.2.4"}}