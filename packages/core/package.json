{"name": "@betswirl/sdk-core", "description": "VanillaJS library for Betswirl protocol", "version": "0.1.13", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/betswirl/sdk.git", "directory": "packages/core"}, "type": "module", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "main": "./dist/index.cjs", "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "sideEffects": false, "scripts": {"gql:cleanup": "rimraf --glob \"./src/data/subgraphs/protocol/documents/**/*.ts\"", "gql": "graphql-codegen", "build": "rimraf ./dist && npm run gql && tsup --tsconfig tsconfig.build.json", "prepublishOnly": "cross-env NODE_ENV=production npm run build"}, "contributors": ["kinco_dev"], "keywords": ["betswirl", "bet", "ethereum", "dapps", "casino", "web3"], "peerDependencies": {"@apollo/client": ">=3.12.11", "typescript": ">=5.0.4", "viem": "catalog:"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "files": ["dist"], "dependencies": {"abitype": "^1.0.8", "decimal.js": "^10.5.0", "graphql-tag": "^2.12.6"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.4", "@graphql-codegen/client-preset": "^4.6.2", "@graphql-codegen/near-operation-file-preset": "^3.0.0", "@graphql-codegen/typescript": "^4.1.3", "@graphql-codegen/typescript-document-nodes": "^4.0.13", "@graphql-codegen/typescript-operations": "^4.4.1", "cross-env": "^7.0.3", "rimraf": "^6.0.1", "tsup": "^8.3.6"}}